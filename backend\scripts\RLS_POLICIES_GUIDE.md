# Hướng dẫn thiết lập Row Level Security (RLS) Policies cho Hospital Management System

## 📋 Tổng quan

Row Level Security (RLS) là một tính năng bảo mật của PostgreSQL/Supabase cho phép kiểm soát quyền truy cập ở cấp độ hàng (row) trong bảng. Điều này đảm bảo rằng người dùng chỉ có thể truy cập dữ liệu mà họ được phép.

## 🔐 Nguyên tắc bảo mật

### Phân quyền theo vai trò:

1. **Admin (`role = 'admin'`)**
   - Toàn quyền truy cập tất cả dữ liệu
   - C<PERSON> thể quản lý tất cả bảng và records
   - <PERSON><PERSON> thể thêm, sửa, x<PERSON><PERSON> b<PERSON>t kỳ dữ liệu nào

2. **Doctor (`role = 'doctor'`)**
   - Truy cập dữ liệu bệnh nhân của họ
   - Quản lý hồ sơ y tế, đ<PERSON><PERSON> thuố<PERSON>, lịch khám
   - Xem thông tin departments, rooms, specialties
   - <PERSON>h<PERSON><PERSON> thể truy cập dữ liệu tài chính trực tiếp

3. **Patient (`role = 'patient'`)**
   - Chỉ truy cập dữ liệu của chính họ
   - Xem hồ sơ y tế, lịch hẹn, hóa đơn của mình
   - Xem thông tin bác sĩ, departments (để đặt lịch)
   - Không thể sửa đổi dữ liệu y tế

4. **Service Role**
   - Toàn quyền cho backend operations
   - Được sử dụng bởi server-side code

## 📁 Cấu trúc Files

```
backend/scripts/
├── setup-rls-policies.sql          # Part 1: Core tables (profiles, doctors, patients, appointments, medical_records)
├── setup-rls-policies-part2.sql    # Part 2: Supporting tables (prescriptions, billing, payments, schedules, etc.)
├── setup-rls-policies-part3.sql    # Part 3: Medical records related & enum tables
└── RLS_POLICIES_GUIDE.md           # Hướng dẫn này
```

## 🚀 Cách thiết lập

### Bước 1: Chạy các file SQL theo thứ tự

1. **Chạy Part 1** - Core tables:
```sql
-- Trong Supabase SQL Editor
-- Copy và paste nội dung từ setup-rls-policies.sql
```

2. **Chạy Part 2** - Supporting tables:
```sql
-- Trong Supabase SQL Editor  
-- Copy và paste nội dung từ setup-rls-policies-part2.sql
```

3. **Chạy Part 3** - Medical records & enum tables:
```sql
-- Trong Supabase SQL Editor
-- Copy và paste nội dung từ setup-rls-policies-part3.sql
```

### Bước 2: Kiểm tra kết quả

```javascript
// Chạy script kiểm tra
node backend/scripts/check-database-status.js
```

## 📊 Chi tiết Policies cho từng bảng

### Core Tables

#### 1. **profiles**
- Users: Xem/sửa profile của chính họ
- Admins: Toàn quyền tất cả profiles
- Service role: Toàn quyền

#### 2. **doctors**
- Doctors: Xem/sửa thông tin của chính họ
- Patients: Xem thông tin bác sĩ (để đặt lịch)
- Admins: Toàn quyền
- Service role: Toàn quyền

#### 3. **patients**
- Patients: Xem/sửa thông tin của chính họ
- Doctors: Xem thông tin bệnh nhân (để chăm sóc)
- Admins: Toàn quyền
- Service role: Toàn quyền

#### 4. **appointments**
- Patients: Quản lý lịch hẹn của chính họ
- Doctors: Xem/sửa lịch hẹn của họ
- Admins: Toàn quyền
- Service role: Toàn quyền

#### 5. **medical_records**
- Patients: Xem hồ sơ y tế của chính họ
- Doctors: Quản lý hồ sơ y tế bệnh nhân
- Admins: Toàn quyền
- Service role: Toàn quyền

### Supporting Tables

#### 6. **prescriptions**
- Patients: Xem đơn thuốc của chính họ
- Doctors: Quản lý đơn thuốc
- Admins: Toàn quyền
- Service role: Toàn quyền

#### 7. **billing**
- Patients: Xem hóa đơn của chính họ
- Doctors: Xem hóa đơn bệnh nhân (read-only)
- Admins: Toàn quyền
- Service role: Toàn quyền

#### 8. **payments**
- Patients: Xem thanh toán của chính họ
- Admins: Toàn quyền
- Service role: Toàn quyền

#### 9. **schedules & availability**
- Doctors: Quản lý lịch làm việc của chính họ
- Patients: Xem lịch bác sĩ (để đặt lịch)
- Admins: Toàn quyền
- Service role: Toàn quyền

#### 10. **departments & rooms**
- All authenticated users: Xem thông tin
- Admins: Toàn quyền
- Service role: Toàn quyền

### Medical Records Related Tables

#### 11. **medical_record_attachments**
- Patients: Xem attachments của hồ sơ y tế của họ
- Doctors & Admins: Quản lý attachments
- Service role: Toàn quyền

#### 12. **lab_results**
- Patients: Xem kết quả xét nghiệm của họ
- Doctors & Admins: Quản lý kết quả xét nghiệm
- Service role: Toàn quyền

#### 13. **vital_signs_history**
- Patients: Xem chỉ số sinh hiệu của họ
- Doctors & Admins: Quản lý chỉ số sinh hiệu
- Service role: Toàn quyền

#### 14. **medical_record_templates**
- All authenticated users: Xem templates đang active
- Doctors: Tạo và quản lý templates của họ
- Admins: Toàn quyền
- Service role: Toàn quyền

### Enum Tables

#### 15. **specialties, room_types, status_values, payment_methods**
- All authenticated users: Xem dữ liệu
- Admins: Toàn quyền
- Service role: Toàn quyền

#### 16. **diagnosis, medications**
- Doctors & Admins: Xem dữ liệu
- Admins: Toàn quyền
- Service role: Toàn quyền

## 🔧 Troubleshooting

### Lỗi thường gặp:

1. **"permission denied for table"**
   - Kiểm tra RLS đã được enable chưa
   - Kiểm tra user có đúng role không

2. **"new row violates row-level security policy"**
   - Kiểm tra policy cho INSERT operation
   - Đảm bảo user có quyền tạo record

3. **"no policy found"**
   - Kiểm tra policies đã được tạo chưa
   - Kiểm tra tên policy có đúng không

### Debug commands:

```sql
-- Kiểm tra RLS status
SELECT schemaname, tablename, rowsecurity 
FROM pg_tables 
WHERE schemaname = 'public';

-- Kiểm tra policies
SELECT schemaname, tablename, policyname, permissive, roles, cmd, qual 
FROM pg_policies 
WHERE schemaname = 'public';

-- Kiểm tra current user role
SELECT auth.uid(), auth.role();
```

## 📝 Lưu ý quan trọng

1. **Luôn test policies** sau khi thiết lập
2. **Backup database** trước khi chạy scripts
3. **Service role** cần được sử dụng cẩn thận
4. **Policies có thể ảnh hưởng đến performance** - cần optimize queries
5. **Enum tables** nên có policies đơn giản vì được truy cập thường xuyên

## 🔄 Cập nhật Policies

Để cập nhật policies, sử dụng pattern:
```sql
DROP POLICY IF EXISTS "policy_name" ON table_name;
CREATE POLICY "policy_name" ON table_name FOR operation USING (condition);
```

Điều này đảm bảo không có conflict khi chạy lại scripts.
