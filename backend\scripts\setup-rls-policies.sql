-- Hospital Management System - Row Level Security (RLS) Policies
-- Thi<PERSON><PERSON> lập các policies bảo mật cho từng bảng trong hệ thống quản lý bệnh viện
--
-- Nguyên tắc bảo mật:
-- - Admin: To<PERSON><PERSON> quyền truy cập tất cả dữ liệu
-- - Doctor: Truy cập dữ liệu bệnh nhân của họ và dữ liệu cá nhân
-- - Patient: Chỉ truy cập dữ liệu của chính họ
-- - Service Role: Toàn quyền cho backend operations

-- ============================================================================
-- ENABLE RLS ON ALL TABLES
-- ============================================================================

-- Enable RLS on core tables
ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE doctors ENABLE ROW LEVEL SECURITY;
ALTER TABLE patients ENABLE ROW LEVEL SECURITY;
ALTER TABLE appointments ENABLE ROW LEVEL SECURITY;
ALTER TABLE departments ENABLE ROW LEVEL SECURITY;
ALTER TABLE rooms ENABLE ROW LEVEL SECURITY;
ALTER TABLE medical_records ENABLE ROW LEVEL SECURITY;
ALTER TABLE prescriptions ENABLE ROW LEVEL SECURITY;
ALTER TABLE billing ENABLE ROW LEVEL SECURITY;
ALTER TABLE payments ENABLE ROW LEVEL SECURITY;
ALTER TABLE schedules ENABLE ROW LEVEL SECURITY;
ALTER TABLE availability ENABLE ROW LEVEL SECURITY;

-- Enable RLS on medical record related tables
DO $$
BEGIN
    -- Check if tables exist before enabling RLS
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'medical_record_attachments') THEN
        ALTER TABLE medical_record_attachments ENABLE ROW LEVEL SECURITY;
    END IF;

    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'lab_results') THEN
        ALTER TABLE lab_results ENABLE ROW LEVEL SECURITY;
    END IF;

    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'vital_signs_history') THEN
        ALTER TABLE vital_signs_history ENABLE ROW LEVEL SECURITY;
    END IF;

    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'medical_record_templates') THEN
        ALTER TABLE medical_record_templates ENABLE ROW LEVEL SECURITY;
    END IF;

    -- Enable RLS on enum tables if they exist
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'specialties') THEN
        ALTER TABLE specialties ENABLE ROW LEVEL SECURITY;
    END IF;

    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'room_types') THEN
        ALTER TABLE room_types ENABLE ROW LEVEL SECURITY;
    END IF;

    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'diagnosis') THEN
        ALTER TABLE diagnosis ENABLE ROW LEVEL SECURITY;
    END IF;

    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'medications') THEN
        ALTER TABLE medications ENABLE ROW LEVEL SECURITY;
    END IF;

    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'status_values') THEN
        ALTER TABLE status_values ENABLE ROW LEVEL SECURITY;
    END IF;

    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'payment_methods') THEN
        ALTER TABLE payment_methods ENABLE ROW LEVEL SECURITY;
    END IF;
END $$;

-- ============================================================================
-- PROFILES TABLE POLICIES
-- ============================================================================

-- Drop existing policies if they exist
DROP POLICY IF EXISTS "Users can view own profile" ON profiles;
DROP POLICY IF EXISTS "Users can update own profile" ON profiles;
DROP POLICY IF EXISTS "Admins can view all profiles" ON profiles;
DROP POLICY IF EXISTS "Admins can manage all profiles" ON profiles;
DROP POLICY IF EXISTS "Service role full access" ON profiles;

-- Users can view their own profile
CREATE POLICY "Users can view own profile" ON profiles
    FOR SELECT USING (auth.uid() = id);

-- Users can update their own profile
CREATE POLICY "Users can update own profile" ON profiles
    FOR UPDATE USING (auth.uid() = id);

-- Admins can view all profiles
CREATE POLICY "Admins can view all profiles" ON profiles
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM profiles 
            WHERE id = auth.uid() AND role = 'admin'
        )
    );

-- Admins can manage all profiles
CREATE POLICY "Admins can manage all profiles" ON profiles
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM profiles 
            WHERE id = auth.uid() AND role = 'admin'
        )
    );

-- Service role has full access
CREATE POLICY "Service role full access" ON profiles
    FOR ALL USING (auth.role() = 'service_role');

-- ============================================================================
-- DOCTORS TABLE POLICIES
-- ============================================================================

-- Drop existing policies
DROP POLICY IF EXISTS "Doctors can view own data" ON doctors;
DROP POLICY IF EXISTS "Doctors can update own data" ON doctors;
DROP POLICY IF EXISTS "Admins can manage doctors" ON doctors;
DROP POLICY IF EXISTS "Patients can view doctor info" ON doctors;
DROP POLICY IF EXISTS "Service role full access doctors" ON doctors;

-- Doctors can view their own data
CREATE POLICY "Doctors can view own data" ON doctors
    FOR SELECT USING (profile_id = auth.uid());

-- Doctors can update their own data
CREATE POLICY "Doctors can update own data" ON doctors
    FOR UPDATE USING (profile_id = auth.uid());

-- Admins can manage all doctors
CREATE POLICY "Admins can manage doctors" ON doctors
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM profiles 
            WHERE id = auth.uid() AND role = 'admin'
        )
    );

-- Patients can view doctor information (for appointments)
CREATE POLICY "Patients can view doctor info" ON doctors
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM profiles 
            WHERE id = auth.uid() AND role = 'patient'
        )
    );

-- Service role has full access
CREATE POLICY "Service role full access doctors" ON doctors
    FOR ALL USING (auth.role() = 'service_role');

-- ============================================================================
-- PATIENTS TABLE POLICIES
-- ============================================================================

-- Drop existing policies
DROP POLICY IF EXISTS "Patients can view own data" ON patients;
DROP POLICY IF EXISTS "Patients can update own data" ON patients;
DROP POLICY IF EXISTS "Doctors can view patient data" ON patients;
DROP POLICY IF EXISTS "Admins can manage patients" ON patients;
DROP POLICY IF EXISTS "Service role full access patients" ON patients;

-- Patients can view their own data
CREATE POLICY "Patients can view own data" ON patients
    FOR SELECT USING (profile_id = auth.uid());

-- Patients can update their own data
CREATE POLICY "Patients can update own data" ON patients
    FOR UPDATE USING (profile_id = auth.uid());

-- Doctors can view patient data (for medical care)
CREATE POLICY "Doctors can view patient data" ON patients
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM profiles 
            WHERE id = auth.uid() AND role = 'doctor'
        )
    );

-- Admins can manage all patients
CREATE POLICY "Admins can manage patients" ON patients
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM profiles 
            WHERE id = auth.uid() AND role = 'admin'
        )
    );

-- Service role has full access
CREATE POLICY "Service role full access patients" ON patients
    FOR ALL USING (auth.role() = 'service_role');

-- ============================================================================
-- APPOINTMENTS TABLE POLICIES
-- ============================================================================

-- Drop existing policies
DROP POLICY IF EXISTS "Users can view own appointments" ON appointments;
DROP POLICY IF EXISTS "Patients can manage own appointments" ON appointments;
DROP POLICY IF EXISTS "Doctors can view their appointments" ON appointments;
DROP POLICY IF EXISTS "Doctors can update their appointments" ON appointments;
DROP POLICY IF EXISTS "Admins can manage all appointments" ON appointments;
DROP POLICY IF EXISTS "Service role full access appointments" ON appointments;

-- Users can view their own appointments (patients and doctors)
CREATE POLICY "Users can view own appointments" ON appointments
    FOR SELECT USING (
        patient_id IN (SELECT patient_id FROM patients WHERE profile_id = auth.uid())
        OR doctor_id IN (SELECT doctor_id FROM doctors WHERE profile_id = auth.uid())
    );

-- Patients can manage their own appointments
CREATE POLICY "Patients can manage own appointments" ON appointments
    FOR ALL USING (
        patient_id IN (SELECT patient_id FROM patients WHERE profile_id = auth.uid())
        AND EXISTS (
            SELECT 1 FROM profiles 
            WHERE id = auth.uid() AND role = 'patient'
        )
    );

-- Doctors can view their appointments
CREATE POLICY "Doctors can view their appointments" ON appointments
    FOR SELECT USING (
        doctor_id IN (SELECT doctor_id FROM doctors WHERE profile_id = auth.uid())
    );

-- Doctors can update their appointments
CREATE POLICY "Doctors can update their appointments" ON appointments
    FOR UPDATE USING (
        doctor_id IN (SELECT doctor_id FROM doctors WHERE profile_id = auth.uid())
    );

-- Admins can manage all appointments
CREATE POLICY "Admins can manage all appointments" ON appointments
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM profiles 
            WHERE id = auth.uid() AND role = 'admin'
        )
    );

-- Service role has full access
CREATE POLICY "Service role full access appointments" ON appointments
    FOR ALL USING (auth.role() = 'service_role');

-- ============================================================================
-- MEDICAL RECORDS TABLE POLICIES
-- ============================================================================

-- Drop existing policies
DROP POLICY IF EXISTS "Patients can view own medical records" ON medical_records;
DROP POLICY IF EXISTS "Doctors can view patient medical records" ON medical_records;
DROP POLICY IF EXISTS "Doctors can manage medical records" ON medical_records;
DROP POLICY IF EXISTS "Admins can manage all medical records" ON medical_records;
DROP POLICY IF EXISTS "Service role full access medical records" ON medical_records;

-- Patients can view their own medical records
CREATE POLICY "Patients can view own medical records" ON medical_records
    FOR SELECT USING (
        patient_id IN (SELECT patient_id FROM patients WHERE profile_id = auth.uid())
    );

-- Doctors can view medical records of their patients
CREATE POLICY "Doctors can view patient medical records" ON medical_records
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM profiles 
            WHERE id = auth.uid() AND role = 'doctor'
        )
    );

-- Doctors can create and update medical records
CREATE POLICY "Doctors can manage medical records" ON medical_records
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM profiles 
            WHERE id = auth.uid() AND role = 'doctor'
        )
    );

-- Admins can manage all medical records
CREATE POLICY "Admins can manage all medical records" ON medical_records
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM profiles 
            WHERE id = auth.uid() AND role = 'admin'
        )
    );

-- Service role has full access
CREATE POLICY "Service role full access medical records" ON medical_records
    FOR ALL USING (auth.role() = 'service_role');

RAISE NOTICE 'RLS policies setup completed for core tables!';
