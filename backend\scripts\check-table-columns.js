/**
 * Check table columns structure
 */

const { createClient } = require('@supabase/supabase-js');
require('dotenv').config();

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || process.env.SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing Supabase configuration');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function checkTableColumns() {
  console.log('🔍 Checking Table Columns Structure...\n');

  const tables = ['appointments', 'medical_records', 'prescriptions'];

  for (const table of tables) {
    console.log(`=== ${table.toUpperCase()} TABLE COLUMNS ===`);

    try {
      // Try to select from table to see if it exists and get structure
      const { data, error } = await supabase
        .from(table)
        .select('*')
        .limit(1);

      if (error) {
        console.error(`❌ Error checking ${table}:`, error.message);
        continue;
      }

      // If we can query the table, check what columns are commonly expected
      console.log(`  ✅ Table ${table} exists and is accessible`);

      // Check for specific columns based on our schema knowledge
      const expectedColumns = getExpectedColumns(table);
      for (const col of expectedColumns) {
        console.log(`  📋 Expected column: ${col}`);
      }

    } catch (err) {
      console.error(`❌ Error checking ${table}:`, err.message);
    }

    console.log('');
  }
}

function getExpectedColumns(tableName) {
  const columns = {
    appointments: [
      'appointment_id', 'patient_id', 'doctor_id',
      'appointment_date', 'appointment_time', 'appointment_datetime',
      'duration_minutes', 'status', 'appointment_type', 'notes',
      'room_id', 'created_at', 'updated_at'
    ],
    medical_records: [
      'record_id', 'patient_id', 'doctor_id', 'appointment_id',
      'visit_date', 'diagnosis', 'treatment', 'notes',
      'status', 'created_at', 'updated_at'
    ],
    prescriptions: [
      'prescription_id', 'patient_id', 'doctor_id', 'medical_record_id',
      'prescription_date', 'status', 'notes', 'total_cost',
      'created_at', 'updated_at'
    ]
  };

  return columns[tableName] || [];
}

checkTableColumns().catch(console.error);
