-- Hospital Management System - Complete RLS Policies Setup
-- <PERSON>ript tổng hợp để thiết lập tất cả RLS policies
-- 
-- Chạy script này trong Supabase SQL Editor để thiết lập toàn bộ RLS policies
-- cho hệ thống quản lý bệnh viện

\echo ''
\echo '=========================================='
\echo 'HOSPITAL MANAGEMENT SYSTEM'
\echo 'ROW LEVEL SECURITY (RLS) POLICIES SETUP'
\echo '=========================================='
\echo ''

-- ============================================================================
-- ENABLE RLS ON ALL TABLES
-- ============================================================================

\echo 'Step 1: Enabling RLS on all tables...'

-- Enable RLS on core tables
ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE doctors ENABLE ROW LEVEL SECURITY;
ALTER TABLE patients ENABLE ROW LEVEL SECURITY;
ALTER TABLE appointments ENABLE ROW LEVEL SECURITY;
ALTER TABLE departments ENABLE ROW LEVEL SECURITY;
ALTER TABLE rooms ENABLE ROW LEVEL SECURITY;
ALTER TABLE medical_records ENABLE ROW LEVEL SECURITY;
ALTER TABLE prescriptions ENABLE ROW LEVEL SECURITY;
ALTER TABLE billing ENABLE ROW LEVEL SECURITY;
ALTER TABLE payments ENABLE ROW LEVEL SECURITY;
ALTER TABLE schedules ENABLE ROW LEVEL SECURITY;
ALTER TABLE availability ENABLE ROW LEVEL SECURITY;

-- Enable RLS on medical record related tables and enum tables
DO $$
BEGIN
    -- Medical record related tables
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'medical_record_attachments') THEN
        ALTER TABLE medical_record_attachments ENABLE ROW LEVEL SECURITY;
    END IF;
    
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'lab_results') THEN
        ALTER TABLE lab_results ENABLE ROW LEVEL SECURITY;
    END IF;
    
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'vital_signs_history') THEN
        ALTER TABLE vital_signs_history ENABLE ROW LEVEL SECURITY;
    END IF;
    
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'medical_record_templates') THEN
        ALTER TABLE medical_record_templates ENABLE ROW LEVEL SECURITY;
    END IF;

    -- Enum tables
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'specialties') THEN
        ALTER TABLE specialties ENABLE ROW LEVEL SECURITY;
    END IF;
    
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'room_types') THEN
        ALTER TABLE room_types ENABLE ROW LEVEL SECURITY;
    END IF;
    
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'diagnosis') THEN
        ALTER TABLE diagnosis ENABLE ROW LEVEL SECURITY;
    END IF;
    
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'medications') THEN
        ALTER TABLE medications ENABLE ROW LEVEL SECURITY;
    END IF;
    
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'status_values') THEN
        ALTER TABLE status_values ENABLE ROW LEVEL SECURITY;
    END IF;
    
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'payment_methods') THEN
        ALTER TABLE payment_methods ENABLE ROW LEVEL SECURITY;
    END IF;
END $$;

\echo 'RLS enabled on all tables!'

-- ============================================================================
-- SETUP POLICIES FOR CORE TABLES
-- ============================================================================

\echo 'Step 2: Setting up policies for core tables...'

-- Include setup-rls-policies.sql content here
\i 'setup-rls-policies.sql'

-- Include setup-rls-policies-part2.sql content here  
\i 'setup-rls-policies-part2.sql'

-- Include setup-rls-policies-part3.sql content here
\i 'setup-rls-policies-part3.sql'

-- ============================================================================
-- VERIFICATION AND SUMMARY
-- ============================================================================

\echo ''
\echo 'Step 3: Verification...'

-- Count policies created
DO $$
DECLARE
    policy_count INTEGER;
    table_count INTEGER;
BEGIN
    -- Count total policies
    SELECT COUNT(*) INTO policy_count
    FROM pg_policies 
    WHERE schemaname = 'public';
    
    -- Count tables with RLS enabled
    SELECT COUNT(*) INTO table_count
    FROM pg_tables 
    WHERE schemaname = 'public' AND rowsecurity = true;
    
    RAISE NOTICE 'RLS SETUP SUMMARY:';
    RAISE NOTICE '- Tables with RLS enabled: %', table_count;
    RAISE NOTICE '- Total policies created: %', policy_count;
    RAISE NOTICE '';
    RAISE NOTICE 'RLS Policies by table:';
    
    -- Show policies per table
    FOR rec IN 
        SELECT tablename, COUNT(*) as policy_count
        FROM pg_policies 
        WHERE schemaname = 'public'
        GROUP BY tablename
        ORDER BY tablename
    LOOP
        RAISE NOTICE '  - %: % policies', rec.tablename, rec.policy_count;
    END LOOP;
END $$;

\echo ''
\echo '=========================================='
\echo 'RLS POLICIES SETUP COMPLETED!'
\echo '=========================================='
\echo ''
\echo 'What was configured:'
\echo '✅ Row Level Security enabled on all tables'
\echo '✅ Policies for user roles (admin, doctor, patient)'
\echo '✅ Service role policies for backend operations'
\echo '✅ Proper access control for medical data'
\echo ''
\echo 'Security Rules Summary:'
\echo '👑 Admin: Full access to all data'
\echo '👨‍⚕️ Doctor: Access to their patients data + own data'
\echo '👤 Patient: Access to their own data only'
\echo '🔧 Service Role: Full backend access'
\echo ''
\echo 'Next steps:'
\echo '1. Test the policies with different user roles'
\echo '2. Run check-database-status.js to verify'
\echo '3. Monitor application functionality'
\echo '4. Check query performance'
\echo ''
\echo 'For troubleshooting, see: RLS_POLICIES_GUIDE.md'
\echo ''
