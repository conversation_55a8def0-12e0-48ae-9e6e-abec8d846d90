# 🏥 Hospital Management - Manual Database Setup Guide

## 📋 Tổng quan

File `SUPABASE_SETUP_FIXED.sql` đã được sửa chữa và cải thiện để hoạt động tốt hơn với database Supabase của bạn.

## 🚀 C<PERSON>ch thực hiện

### Phương pháp 1: Ch<PERSON>y trực tiếp trong Supabase (Khuyến nghị)

1. **Mở Supabase Dashboard**
   - Truy cập: https://supabase.com/dashboard
   - Chọn project của bạn

2. **Vào SQL Editor**
   - Sidebar → SQL Editor
   - Tạo new query

3. **Copy và chạy script**
   - Copy toàn bộ nội dung file `backend/scripts/SUPABASE_SETUP_FIXED.sql`
   - Paste vào SQL Editor
   - Click "Run" để thực thi

### Phương pháp 2: Ch<PERSON><PERSON> từng phần (Nếu script quá lớn)

Nếu script qu<PERSON> lớn, chia thành các phần:

#### Phần 1: Helper Functions (Dòng 6-98)
```sql
-- STEP 1: CREATE HELPER FUNCTIONS
-- Copy từ dòng 6 đến 98
```

#### Phần 2: Foreign Keys (Dòng 100-324)
```sql
-- STEP 2: ADD FOREIGN KEY RELATIONSHIPS
-- Copy từ dòng 100 đến 324
```

#### Phần 3: RLS Policies (Dòng 326-568)
```sql
-- STEP 3: ENABLE RLS AND CREATE POLICIES
-- Copy từ dòng 326 đến 568
```

#### Phần 4: Performance Indexes (Dòng 570-809)
```sql
-- STEP 4: ADD PERFORMANCE INDEXES
-- Copy từ dòng 570 đến 809
```

## ✅ Các cải tiến đã thực hiện

### 🔧 Kiểm tra cột tồn tại
- Thêm kiểm tra `IF EXISTS` trước khi tạo indexes
- Xử lý trường hợp cột không tồn tại
- Tránh lỗi SQL khi schema khác với mong đợi

### 📊 Cải thiện Indexes
- **Appointments**: Xử lý cả `appointment_date` và `appointment_datetime`
- **Prescriptions**: Fallback từ `prescription_date` sang `created_at`
- **Departments/Rooms**: Kiểm tra cột optional trước khi tạo index

### 🔒 RLS Policies
- Policies cho tất cả user roles (admin, doctor, patient)
- Service role có full access cho backend operations
- Bảo mật cấp dòng cho từng bảng

### 🔗 Foreign Keys
- Liên kết đúng giữa các bảng
- CASCADE và SET NULL rules phù hợp
- Kiểm tra tồn tại constraint trước khi tạo

## 🔍 Kiểm tra sau khi setup

Sau khi chạy script, kiểm tra bằng cách:

```bash
# Kiểm tra trạng thái database
node backend/scripts/check-database-status.js
```

Hoặc chạy các query verification trong SQL Editor:

```sql
-- Kiểm tra foreign keys
SELECT * FROM get_foreign_keys();

-- Kiểm tra RLS policies  
SELECT * FROM get_rls_policies();

-- Kiểm tra indexes
SELECT * FROM get_table_indexes();
```

## ⚠️ Lưu ý quan trọng

1. **Backup database** trước khi chạy script
2. **Chạy trong môi trường test** trước
3. **Kiểm tra permissions** của service role key
4. **Monitor performance** sau khi setup

## 🆘 Troubleshooting

### Lỗi "function does not exist"
- Chạy lại phần Helper Functions trước
- Đảm bảo có quyền CREATE FUNCTION

### Lỗi "column does not exist"  
- Script đã được cải thiện để xử lý trường hợp này
- Kiểm tra schema thực tế của bảng

### Lỗi "constraint already exists"
- Script sử dụng `IF NOT EXISTS` để tránh lỗi này
- An toàn để chạy lại nhiều lần

## 📞 Hỗ trợ

Nếu gặp vấn đề, hãy:
1. Kiểm tra logs trong Supabase Dashboard
2. Chạy từng phần để xác định lỗi
3. Đảm bảo service role key có đủ quyền
