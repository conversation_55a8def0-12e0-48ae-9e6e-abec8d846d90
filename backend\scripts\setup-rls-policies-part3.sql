-- Hospital Management System - RLS Policies Part 3
-- Thi<PERSON><PERSON> lập policies cho các bảng medical records và enum tables

-- ============================================================================
-- VITAL SIGNS HISTORY TABLE POLICIES
-- ============================================================================

-- Vital Signs History
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'vital_signs_history') THEN
        -- Drop existing policies
        DROP POLICY IF EXISTS "Users can view related vital signs" ON vital_signs_history;
        DROP POLICY IF EXISTS "Doctors can manage vital signs" ON vital_signs_history;
        DROP POLICY IF EXISTS "Admins can manage all vital signs" ON vital_signs_history;
        DROP POLICY IF EXISTS "Service role full access vital signs" ON vital_signs_history;

        -- Users can view vital signs of their medical records
        CREATE POLICY "Users can view related vital signs" ON vital_signs_history
            FOR SELECT USING (
                record_id IN (
                    SELECT record_id FROM medical_records 
                    WHERE patient_id IN (
                        SELECT patient_id FROM patients WHERE profile_id = auth.uid()
                    )
                )
                OR EXISTS (
                    SELECT 1 FROM profiles 
                    WHERE id = auth.uid() AND role IN ('doctor', 'admin')
                )
            );

        -- Doctors can manage vital signs
        CREATE POLICY "Doctors can manage vital signs" ON vital_signs_history
            FOR ALL USING (
                EXISTS (
                    SELECT 1 FROM profiles 
                    WHERE id = auth.uid() AND role = 'doctor'
                )
            );

        -- Admins can manage all vital signs
        CREATE POLICY "Admins can manage all vital signs" ON vital_signs_history
            FOR ALL USING (
                EXISTS (
                    SELECT 1 FROM profiles 
                    WHERE id = auth.uid() AND role = 'admin'
                )
            );

        -- Service role has full access
        CREATE POLICY "Service role full access vital signs" ON vital_signs_history
            FOR ALL USING (auth.role() = 'service_role');
    END IF;
END $$;

-- ============================================================================
-- MEDICAL RECORD TEMPLATES TABLE POLICIES
-- ============================================================================

-- Medical Record Templates
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'medical_record_templates') THEN
        -- Drop existing policies
        DROP POLICY IF EXISTS "All authenticated users can view templates" ON medical_record_templates;
        DROP POLICY IF EXISTS "Doctors can manage templates" ON medical_record_templates;
        DROP POLICY IF EXISTS "Admins can manage all templates" ON medical_record_templates;
        DROP POLICY IF EXISTS "Service role full access templates" ON medical_record_templates;

        -- All authenticated users can view active templates
        CREATE POLICY "All authenticated users can view templates" ON medical_record_templates
            FOR SELECT USING (
                auth.role() = 'authenticated' AND is_active = true
            );

        -- Doctors can create and manage their own templates
        CREATE POLICY "Doctors can manage templates" ON medical_record_templates
            FOR ALL USING (
                EXISTS (
                    SELECT 1 FROM profiles 
                    WHERE id = auth.uid() AND role = 'doctor'
                )
            );

        -- Admins can manage all templates
        CREATE POLICY "Admins can manage all templates" ON medical_record_templates
            FOR ALL USING (
                EXISTS (
                    SELECT 1 FROM profiles 
                    WHERE id = auth.uid() AND role = 'admin'
                )
            );

        -- Service role has full access
        CREATE POLICY "Service role full access templates" ON medical_record_templates
            FOR ALL USING (auth.role() = 'service_role');
    END IF;
END $$;

-- ============================================================================
-- ENUM TABLES POLICIES (READ-ONLY FOR MOST USERS)
-- ============================================================================

-- Specialties Table
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'specialties') THEN
        -- Drop existing policies
        DROP POLICY IF EXISTS "All authenticated users can view specialties" ON specialties;
        DROP POLICY IF EXISTS "Admins can manage specialties" ON specialties;
        DROP POLICY IF EXISTS "Service role full access specialties" ON specialties;

        -- All authenticated users can view specialties
        CREATE POLICY "All authenticated users can view specialties" ON specialties
            FOR SELECT USING (auth.role() = 'authenticated');

        -- Admins can manage specialties
        CREATE POLICY "Admins can manage specialties" ON specialties
            FOR ALL USING (
                EXISTS (
                    SELECT 1 FROM profiles 
                    WHERE id = auth.uid() AND role = 'admin'
                )
            );

        -- Service role has full access
        CREATE POLICY "Service role full access specialties" ON specialties
            FOR ALL USING (auth.role() = 'service_role');
    END IF;
END $$;

-- Room Types Table
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'room_types') THEN
        -- Drop existing policies
        DROP POLICY IF EXISTS "All authenticated users can view room types" ON room_types;
        DROP POLICY IF EXISTS "Admins can manage room types" ON room_types;
        DROP POLICY IF EXISTS "Service role full access room types" ON room_types;

        -- All authenticated users can view room types
        CREATE POLICY "All authenticated users can view room types" ON room_types
            FOR SELECT USING (auth.role() = 'authenticated');

        -- Admins can manage room types
        CREATE POLICY "Admins can manage room types" ON room_types
            FOR ALL USING (
                EXISTS (
                    SELECT 1 FROM profiles 
                    WHERE id = auth.uid() AND role = 'admin'
                )
            );

        -- Service role has full access
        CREATE POLICY "Service role full access room types" ON room_types
            FOR ALL USING (auth.role() = 'service_role');
    END IF;
END $$;

-- Diagnosis Table
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'diagnosis') THEN
        -- Drop existing policies
        DROP POLICY IF EXISTS "Doctors can view diagnosis" ON diagnosis;
        DROP POLICY IF EXISTS "Admins can manage diagnosis" ON diagnosis;
        DROP POLICY IF EXISTS "Service role full access diagnosis" ON diagnosis;

        -- Doctors and admins can view diagnosis
        CREATE POLICY "Doctors can view diagnosis" ON diagnosis
            FOR SELECT USING (
                EXISTS (
                    SELECT 1 FROM profiles 
                    WHERE id = auth.uid() AND role IN ('doctor', 'admin')
                )
            );

        -- Admins can manage diagnosis
        CREATE POLICY "Admins can manage diagnosis" ON diagnosis
            FOR ALL USING (
                EXISTS (
                    SELECT 1 FROM profiles 
                    WHERE id = auth.uid() AND role = 'admin'
                )
            );

        -- Service role has full access
        CREATE POLICY "Service role full access diagnosis" ON diagnosis
            FOR ALL USING (auth.role() = 'service_role');
    END IF;
END $$;

-- Medications Table
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'medications') THEN
        -- Drop existing policies
        DROP POLICY IF EXISTS "Doctors can view medications" ON medications;
        DROP POLICY IF EXISTS "Admins can manage medications" ON medications;
        DROP POLICY IF EXISTS "Service role full access medications" ON medications;

        -- Doctors and admins can view medications
        CREATE POLICY "Doctors can view medications" ON medications
            FOR SELECT USING (
                EXISTS (
                    SELECT 1 FROM profiles 
                    WHERE id = auth.uid() AND role IN ('doctor', 'admin')
                )
            );

        -- Admins can manage medications
        CREATE POLICY "Admins can manage medications" ON medications
            FOR ALL USING (
                EXISTS (
                    SELECT 1 FROM profiles 
                    WHERE id = auth.uid() AND role = 'admin'
                )
            );

        -- Service role has full access
        CREATE POLICY "Service role full access medications" ON medications
            FOR ALL USING (auth.role() = 'service_role');
    END IF;
END $$;

-- Status Values Table
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'status_values') THEN
        -- Drop existing policies
        DROP POLICY IF EXISTS "All authenticated users can view status values" ON status_values;
        DROP POLICY IF EXISTS "Admins can manage status values" ON status_values;
        DROP POLICY IF EXISTS "Service role full access status values" ON status_values;

        -- All authenticated users can view status values
        CREATE POLICY "All authenticated users can view status values" ON status_values
            FOR SELECT USING (auth.role() = 'authenticated');

        -- Admins can manage status values
        CREATE POLICY "Admins can manage status values" ON status_values
            FOR ALL USING (
                EXISTS (
                    SELECT 1 FROM profiles 
                    WHERE id = auth.uid() AND role = 'admin'
                )
            );

        -- Service role has full access
        CREATE POLICY "Service role full access status values" ON status_values
            FOR ALL USING (auth.role() = 'service_role');
    END IF;
END $$;

-- Payment Methods Table
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'payment_methods') THEN
        -- Drop existing policies
        DROP POLICY IF EXISTS "All authenticated users can view payment methods" ON payment_methods;
        DROP POLICY IF EXISTS "Admins can manage payment methods" ON payment_methods;
        DROP POLICY IF EXISTS "Service role full access payment methods" ON payment_methods;

        -- All authenticated users can view payment methods
        CREATE POLICY "All authenticated users can view payment methods" ON payment_methods
            FOR SELECT USING (auth.role() = 'authenticated');

        -- Admins can manage payment methods
        CREATE POLICY "Admins can manage payment methods" ON payment_methods
            FOR ALL USING (
                EXISTS (
                    SELECT 1 FROM profiles 
                    WHERE id = auth.uid() AND role = 'admin'
                )
            );

        -- Service role has full access
        CREATE POLICY "Service role full access payment methods" ON payment_methods
            FOR ALL USING (auth.role() = 'service_role');
    END IF;
END $$;

RAISE NOTICE 'RLS policies part 3 setup completed for medical records and enum tables!';
