// =====================================================
// ENUM SERVICE - SIMPLIFIED FOR 7 SEPARATE TABLES
// =====================================================

import { supabaseClient } from '@/lib/supabase-client';
import {
  EnumOption,
  Language,
  Specialty,
  DepartmentEnum,
  RoomType,
  Diagnosis,
  Medication,
  StatusValue,
  PaymentMethod,
  BaseEnum,
  getEnumDisplayName,
} from '@/lib/types/enum.types';

class EnumService {
  // =====================================================
  // DIRECT TABLE ACCESS METHODS
  // =====================================================

  async getSpecialties(): Promise<Specialty[]> {
    try {
      const { data, error } = await supabaseClient
        .from('specialties')
        .select('*')
        .eq('is_active', true)
        .order('sort_order');

      if (error) throw error;
      return data || [];
    } catch (error) {
      console.error('Error fetching specialties:', error);
      throw error;
    }
  }

  async getDepartments(): Promise<DepartmentEnum[]> {
    try {
      const { data, error } = await supabaseClient
        .from('departments_enum')
        .select('*')
        .eq('is_active', true)
        .order('sort_order');

      if (error) throw error;
      return data || [];
    } catch (error) {
      console.error('Error fetching departments:', error);
      throw error;
    }
  }

  async getRoomTypes(): Promise<RoomType[]> {
    try {
      const { data, error } = await supabaseClient
        .from('room_types')
        .select('*')
        .eq('is_active', true)
        .order('sort_order');

      if (error) throw error;
      return data || [];
    } catch (error) {
      console.error('Error fetching room types:', error);
      throw error;
    }
  }

  async getDiagnoses(): Promise<Diagnosis[]> {
    try {
      const { data, error } = await supabaseClient
        .from('diagnosis')
        .select('*')
        .eq('is_active', true)
        .order('sort_order');

      if (error) throw error;
      return data || [];
    } catch (error) {
      console.error('Error fetching diagnoses:', error);
      throw error;
    }
  }

  async getMedications(): Promise<Medication[]> {
    try {
      const { data, error } = await supabaseClient
        .from('medications')
        .select('*')
        .eq('is_active', true)
        .order('sort_order');

      if (error) throw error;
      return data || [];
    } catch (error) {
      console.error('Error fetching medications:', error);
      throw error;
    }
  }

  async getStatusValues(): Promise<StatusValue[]> {
    try {
      const { data, error } = await supabaseClient
        .from('status_values')
        .select('*')
        .eq('is_active', true)
        .order('sort_order');

      if (error) throw error;
      return data || [];
    } catch (error) {
      console.error('Error fetching status values:', error);
      throw error;
    }
  }

  async getPaymentMethods(): Promise<PaymentMethod[]> {
    try {
      const { data, error } = await supabaseClient
        .from('payment_methods')
        .select('*')
        .eq('is_active', true)
        .order('sort_order');

      if (error) throw error;
      return data || [];
    } catch (error) {
      console.error('Error fetching payment methods:', error);
      throw error;
    }
  }

  // =====================================================
  // UTILITY METHODS - CONVERT TO OPTIONS
  // =====================================================

  private convertToOptions(items: BaseEnum[], language: Language = 'vi'): EnumOption[] {
    return items.map(item => ({
      value: item.id,
      label: getEnumDisplayName(item, language),
      description: language === 'en' ? item.description_en : item.description_vi,
      color: item.color_code,
      icon: item.icon_name,
    }));
  }

  async getSpecialtyOptions(language: Language = 'vi'): Promise<EnumOption[]> {
    const specialties = await this.getSpecialties();
    return this.convertToOptions(specialties, language);
  }

  async getDepartmentOptions(language: Language = 'vi'): Promise<EnumOption[]> {
    const departments = await this.getDepartments();
    return this.convertToOptions(departments, language);
  }

  async getRoomTypeOptions(language: Language = 'vi'): Promise<EnumOption[]> {
    const roomTypes = await this.getRoomTypes();
    return this.convertToOptions(roomTypes, language);
  }

  async getDiagnosisOptions(language: Language = 'vi'): Promise<EnumOption[]> {
    const diagnoses = await this.getDiagnoses();
    return this.convertToOptions(diagnoses, language);
  }

  async getMedicationOptions(language: Language = 'vi'): Promise<EnumOption[]> {
    const medications = await this.getMedications();
    return this.convertToOptions(medications, language);
  }

  async getStatusOptions(language: Language = 'vi', appliesTo?: string): Promise<EnumOption[]> {
    const statusValues = await this.getStatusValues();
    let filtered = statusValues;

    if (appliesTo) {
      filtered = statusValues.filter(item =>
        !item.applies_to || item.applies_to === appliesTo
      );
    }

    return this.convertToOptions(filtered, language);
  }

  async getPaymentMethodOptions(language: Language = 'vi'): Promise<EnumOption[]> {
    const paymentMethods = await this.getPaymentMethods();
    return this.convertToOptions(paymentMethods, language);
  }

}

// Export singleton instance
export const enumService = new EnumService();
