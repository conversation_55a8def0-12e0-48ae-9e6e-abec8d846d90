# 🚀 Hướng dẫn nhanh thiết lập RLS Policies

## 📋 Tóm tắt

Thiết lập Row Level Security (RLS) cho hệ thống quản lý bệnh viện với 4 vai trò chính:
- **Admin**: <PERSON><PERSON><PERSON> quyền
- **Doctor**: <PERSON><PERSON><PERSON> cập dữ liệu bệnh nhân của họ
- **Patient**: Chỉ truy cập dữ liệu của chính họ  
- **Service Role**: Backend operations

## ⚡ Thiết lập nhanh (Khuyến nghị)

### Cách 1: Chạy script tổng hợp

1. Mở **Supabase SQL Editor**
2. Copy toàn bộ nội dung từ file `setup-all-rls-policies.sql`
3. Paste và chạy

```sql
-- Hoặc nếu hỗ trợ \i command
\i 'setup-all-rls-policies.sql'
```

### Cách 2: <PERSON><PERSON><PERSON> từ<PERSON> phầ<PERSON> (<PERSON><PERSON><PERSON> cách 1 lỗi)

1. **Part 1 - Core tables:**
   ```sql
   -- Co<PERSON> nội dung từ setup-rls-policies.sql và chạy
   ```

2. **Part 2 - Supporting tables:**
   ```sql
   -- Copy nội dung từ setup-rls-policies-part2.sql và chạy
   ```

3. **Part 3 - Medical records & enum tables:**
   ```sql
   -- Copy nội dung từ setup-rls-policies-part3.sql và chạy
   ```

## ✅ Kiểm tra kết quả

### Trong Supabase Dashboard:
1. Vào **Database** → **Tables**
2. Chọn bất kỳ bảng nào → **RLS** tab
3. Kiểm tra policies đã được tạo

### Bằng SQL:
```sql
-- Kiểm tra RLS status
SELECT schemaname, tablename, rowsecurity 
FROM pg_tables 
WHERE schemaname = 'public' AND rowsecurity = true;

-- Kiểm tra số lượng policies
SELECT tablename, COUNT(*) as policy_count
FROM pg_policies 
WHERE schemaname = 'public'
GROUP BY tablename
ORDER BY tablename;
```

### Bằng script Node.js:
```bash
cd backend
node scripts/check-database-status.js
```

## 🔧 Test policies

### Test với role Patient:
```sql
-- Set role (trong app thực tế, auth.uid() sẽ tự động set)
SET LOCAL rls.user_id = 'patient-uuid-here';

-- Test query - chỉ thấy dữ liệu của patient này
SELECT * FROM appointments;
SELECT * FROM medical_records;
```

### Test với role Doctor:
```sql
SET LOCAL rls.user_id = 'doctor-uuid-here';

-- Test query - thấy dữ liệu bệnh nhân của doctor này
SELECT * FROM patients;
SELECT * FROM appointments;
```

## 🚨 Troubleshooting

### Lỗi "permission denied":
1. Kiểm tra RLS đã enable chưa
2. Kiểm tra user có đúng role trong bảng `profiles`
3. Kiểm tra policies đã được tạo chưa

### Lỗi "no policy found":
1. Chạy lại scripts setup policies
2. Kiểm tra tên bảng có đúng không

### Performance chậm:
1. Kiểm tra indexes đã được tạo chưa
2. Optimize queries với EXPLAIN ANALYZE

## 📚 Tài liệu chi tiết

- **RLS_POLICIES_GUIDE.md**: Hướng dẫn chi tiết đầy đủ
- **README.md**: Tổng quan về database setup
- **setup-*.sql**: Source code các policies

## 🔄 Cập nhật policies

Để thay đổi policies:
1. Sửa file SQL tương ứng
2. Chạy lại script (policies sẽ được DROP và CREATE lại)
3. Test lại functionality

## 📞 Hỗ trợ

Nếu gặp vấn đề:
1. Kiểm tra logs trong Supabase Dashboard
2. Xem file RLS_POLICIES_GUIDE.md để debug
3. Test với Service Role trước để đảm bảo data structure đúng
