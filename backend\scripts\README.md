# Hospital Management System - Database Setup Scripts

Bộ scripts để thiết lập và quản lý database Supabase cho hệ thống quản lý bệnh viện.

## 📋 Tổng quan

Scripts này sẽ thiết lập:
- ✅ **Foreign Key Relationships** - <PERSON><PERSON><PERSON> liên hệ giữa các bảng
- ✅ **Row Level Security (RLS) Policies** - B<PERSON><PERSON> mật cấp dòng
- ✅ **Performance Indexes** - Tối ưu hiệu suất truy vấn
- ✅ **Helper Functions** - Các function hỗ trợ monitoring

## 🚀 Cách sử dụng

### 1. Kiểm tra trạng thái database hiện tại

```bash
# Thiết lập environment variables
export NEXT_PUBLIC_SUPABASE_URL="your-supabase-url"
export SUPABASE_SERVICE_ROLE_KEY="your-service-role-key"

# Kiểm tra database
node backend/scripts/check-database-status.js
```

### 2. Chạy setup tự động (<PERSON><PERSON><PERSON><PERSON><PERSON> nghị)

```bash
# Chạy toàn bộ setup
node backend/scripts/run-database-setup.js
```

### 3. Ch<PERSON>y từng bước thủ công

Nếu muốn kiểm soát từng bước:

#### Bước 1: Tạo helper functions
```sql
-- Chạy trong Supabase SQL Editor
\i backend/scripts/database-helper-functions.sql
```

#### Bước 2: Thêm foreign keys
```sql
\i backend/scripts/add-foreign-keys.sql
```

#### Bước 3: Thiết lập RLS policies
```sql
\i backend/scripts/setup-rls-policies.sql
\i backend/scripts/setup-rls-policies-part2.sql
```

#### Bước 4: Thêm performance indexes
```sql
\i backend/scripts/add-performance-indexes.sql
```

## 📁 Cấu trúc Files

```
backend/scripts/
├── README.md                           # Hướng dẫn này
├── check-database-status.js            # Kiểm tra trạng thái database
├── run-database-setup.js               # Script chạy tự động
├── database-helper-functions.sql       # Helper functions
├── add-foreign-keys.sql                # Foreign key relationships
├── setup-rls-policies.sql              # RLS policies (Part 1)
├── setup-rls-policies-part2.sql        # RLS policies (Part 2)
├── add-performance-indexes.sql         # Performance indexes
└── setup-complete-database.sql         # Script tổng hợp (manual)
```

## 🔗 Foreign Key Relationships

### Core Relationships
- `doctors.profile_id` → `profiles.id`
- `doctors.department_id` → `departments.department_id`
- `patients.profile_id` → `profiles.id`
- `appointments.patient_id` → `patients.patient_id`
- `appointments.doctor_id` → `doctors.doctor_id`
- `appointments.room_id` → `rooms.room_id`
- `medical_records.patient_id` → `patients.patient_id`
- `medical_records.doctor_id` → `doctors.doctor_id`
- `medical_records.appointment_id` → `appointments.appointment_id`

### Medical Records Relationships
- `medical_record_attachments.record_id` → `medical_records.record_id`
- `lab_results.record_id` → `medical_records.record_id`
- `vital_signs_history.record_id` → `medical_records.record_id`

### Billing Relationships
- `billing.patient_id` → `patients.patient_id`
- `billing.appointment_id` → `appointments.appointment_id`
- `payments.bill_id` → `billing.bill_id`

## 🔒 RLS Policies

### User Roles
- **Admin**: Full access to all data
- **Doctor**: Access to their patients' data and their own data
- **Patient**: Access to their own data only

### Policy Examples
```sql
-- Patients can view their own medical records
CREATE POLICY "Patients can view own medical records" ON medical_records
    FOR SELECT USING (
        patient_id IN (SELECT patient_id FROM patients WHERE profile_id = auth.uid())
    );

-- Doctors can manage medical records
CREATE POLICY "Doctors can manage medical records" ON medical_records
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM profiles 
            WHERE id = auth.uid() AND role = 'doctor'
        )
    );
```

## 🔒 Row Level Security (RLS) Policies

### Files Structure
```
scripts/
├── setup-rls-policies.sql          # Part 1: Core tables
├── setup-rls-policies-part2.sql    # Part 2: Supporting tables
├── setup-rls-policies-part3.sql    # Part 3: Medical records & enum tables
├── setup-all-rls-policies.sql      # Complete setup script
└── RLS_POLICIES_GUIDE.md           # Detailed guide
```

### Security Rules
- **Admin**: Full access to all data
- **Doctor**: Access to their patients' data + own data
- **Patient**: Access to their own data only
- **Service Role**: Full backend access

### Quick Setup
```sql
-- Run in Supabase SQL Editor
\i 'setup-all-rls-policies.sql'
```

### Manual Setup (Step by step)
```sql
-- Step 1: Core tables
\i 'setup-rls-policies.sql'

-- Step 2: Supporting tables
\i 'setup-rls-policies-part2.sql'

-- Step 3: Medical records & enum tables
\i 'setup-rls-policies-part3.sql'
```

## 📊 Performance Indexes

### Key Indexes
- **profiles**: role, email_verified, is_active
- **doctors**: profile_id, specialization, status, department_id
- **patients**: profile_id, date_of_birth, gender
- **appointments**: patient_id, doctor_id, appointment_date, status
- **medical_records**: patient_id, doctor_id, visit_date

### Composite Indexes
- `appointments(doctor_id, appointment_date)`
- `medical_records(patient_id, visit_date)`
- `billing(patient_id, status)`

## 🛠️ Helper Functions

### Available Functions
- `get_foreign_keys()` - Lấy danh sách foreign keys
- `get_rls_policies()` - Lấy danh sách RLS policies
- `get_table_indexes()` - Lấy danh sách indexes
- `table_exists(table_name)` - Kiểm tra table có tồn tại
- `get_table_row_count(table_name)` - Đếm số rows trong table

### Usage Example
```sql
-- Xem tất cả foreign keys
SELECT * FROM get_foreign_keys();

-- Xem RLS policies
SELECT * FROM get_rls_policies();

-- Xem indexes
SELECT * FROM get_table_indexes();
```

## ⚠️ Lưu ý quan trọng

### Trước khi chạy
1. **Backup database** trước khi thực hiện
2. **Kiểm tra environment variables** đã được thiết lập
3. **Đảm bảo có quyền service_role** trong Supabase

### Sau khi chạy
1. **Test tất cả user roles** (admin, doctor, patient)
2. **Kiểm tra performance** của các queries
3. **Monitor RLS policies** hoạt động đúng

### Troubleshooting
- Nếu có lỗi foreign key: Kiểm tra dữ liệu có tồn tại trong bảng reference
- Nếu RLS policies không hoạt động: Kiểm tra auth context
- Nếu performance chậm: Review và optimize indexes

## 🔍 Monitoring

### Kiểm tra định kỳ
```bash
# Kiểm tra trạng thái database
node backend/scripts/check-database-status.js

# Kiểm tra performance
SELECT * FROM get_table_indexes() WHERE tablename = 'appointments';

# Kiểm tra RLS policies
SELECT * FROM get_rls_policies() WHERE tablename = 'medical_records';
```

### Metrics quan trọng
- Số lượng foreign keys
- Số lượng RLS policies
- Số lượng indexes
- Row count của các bảng chính

## 📞 Hỗ trợ

Nếu gặp vấn đề:
1. Kiểm tra logs trong console
2. Kiểm tra Supabase dashboard
3. Review RLS policies và permissions
4. Kiểm tra foreign key constraints

---

**Lưu ý**: Scripts này được thiết kế để an toàn và có thể chạy nhiều lần mà không gây lỗi.
